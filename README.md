# العقل المبدع - Creative Mind

تطبيق الذكاء الاصطناعي للإبداع والإلهام

## الوصف

العقل المبدع هو تطبيق Flutter يستخدم الذكاء الاصطناعي لتوليد أفكار إبداعية في ستة مجالات مختلفة:

- 📖 **قصة**: إنشاء قصص مشوقة ومتفاعلة
- 🍳 **مطبخ**: وصفات طبخ مبتكرة من المكونات المتوفرة
- 🏥 **صحة**: نصائح صحية وطرق علاج طبيعية
- 🎓 **تعليم**: شرح المواضيع التعليمية بطرق مبتكرة
- 💼 **مشاريع**: أفكار مشاريع استثمارية مربحة
- 🤝 **تعاملات**: استراتيجيات التواصل والتعامل الاجتماعي

## الميزات

- ✨ واجهة مستخدم جميلة ومتجاوبة
- 🎨 تأثيرات بصرية وخلفيات متحركة
- 🤖 ذكاء اصطناعي متقدم (Gemini AI)
- 📱 محسن للهواتف المحمولة
- 🗂️ نظام حفظ واسترجاع الأفكار
- 🔄 اقتراحات تلقائية ذكية
- 🌙 تصميم داكن أنيق
- 🇸🇦 دعم كامل للغة العربية

## متطلبات التشغيل

### للتطوير:
- Flutter SDK 3.0+
- Android Studio / VS Code
- Android SDK
- Java 8+

### للاستخدام:
- Android 5.0+ (API level 21)
- 100 MB مساحة تخزين
- اتصال بالإنترنت

## التثبيت والتشغيل

### 1. تثبيت التبعيات
```bash
flutter pub get
```

### 2. تشغيل التطبيق في وضع التطوير
```bash
flutter run
```

### 3. بناء APK للإنتاج
```bash
# تشغيل ملف البناء التلقائي
build_apk.bat

# أو يدوياً
flutter build apk --release
```

## بناء APK

### الطريقة السهلة:
1. تشغيل ملف `build_apk.bat`
2. انتظار انتهاء البناء
3. العثور على الملف في: `build\app\outputs\flutter-apk\app-release.apk`

### الطريقة اليدوية:
```bash
# تنظيف المشروع
flutter clean

# تحديث التبعيات
flutter pub get

# بناء APK
flutter build apk --release --target-platform android-arm,android-arm64,android-x64
```

## هيكل المشروع

```
lib/
├── main.dart                 # نقطة البداية
├── models/                   # نماذج البيانات
│   ├── category.dart
│   └── idea.dart
├── providers/                # إدارة الحالة
│   └── app_provider.dart
├── screens/                  # الشاشات
│   ├── home_screen.dart
│   └── history_screen.dart
├── services/                 # الخدمات
│   ├── ai_service.dart
│   └── storage_service.dart
├── utils/                    # الأدوات المساعدة
│   └── theme.dart
└── widgets/                  # المكونات
    ├── animated_background.dart
    ├── category_selector.dart
    ├── input_section.dart
    ├── results_section.dart
    ├── history_button.dart
    └── preloader.dart
```

## التكوين

### مفتاح API:
- يتم استخدام Gemini AI API
- المفتاح موجود في `lib/services/ai_service.dart`
- يمكن تغييره حسب الحاجة

### إعدادات التطبيق:
- اسم التطبيق: `android/app/src/main/AndroidManifest.xml`
- أيقونة التطبيق: `android/app/src/main/res/`
- إعدادات البناء: `android/app/build.gradle`

## الاستخدام

1. **اختيار القسم**: اختر أحد الأقسام الستة
2. **إدخال الموضوع**: اكتب الموضوع أو الفكرة
3. **الحصول على الإلهام**: اضغط "ألهمني" للحصول على أفكار
4. **التوسع**: اضغط على أي فكرة للحصول على تفاصيل أكثر
5. **الحفظ**: يتم حفظ الأفكار تلقائياً في السجل

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

للدعم والاستفسارات:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني

---

**العقل المبدع - حيث تولد الأفكار العظيمة** ✨
