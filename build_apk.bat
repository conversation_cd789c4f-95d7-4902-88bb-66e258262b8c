@echo off
echo ========================================
echo Building Creative Mind APK
echo ========================================

echo.
echo [1/4] Cleaning previous builds...
flutter clean

echo.
echo [2/4] Getting dependencies...
flutter pub get

echo.
echo [3/4] Building APK...
flutter build apk --release --target-platform android-arm,android-arm64,android-x64

echo.
echo [4/4] APK built successfully!
echo.
echo APK Location: build\app\outputs\flutter-apk\app-release.apk
echo.

if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo ========================================
    echo SUCCESS: APK file created successfully!
    echo ========================================
    echo.
    echo File: build\app\outputs\flutter-apk\app-release.apk
    echo Size: 
    dir "build\app\outputs\flutter-apk\app-release.apk" | findstr "app-release.apk"
    echo.
    echo You can now install this APK on your Android device.
) else (
    echo ========================================
    echo ERROR: APK build failed!
    echo ========================================
    echo Please check the error messages above.
)

echo.
echo Press any key to exit...
pause > nul
