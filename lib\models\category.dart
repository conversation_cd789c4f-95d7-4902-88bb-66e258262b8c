import 'package:flutter/material.dart';

class Category {
  final String key;
  final String name;
  final IconData icon;
  final String placeholder;
  final List<String> suggestions;
  final List<String> autoSuggestions;
  final String mainPrompt;
  final String expandPrompt;
  final String expandButtonText;
  final Color primaryColor;
  final Color secondaryColor;
  final String iconEmoji;

  const Category({
    required this.key,
    required this.name,
    required this.icon,
    required this.placeholder,
    required this.suggestions,
    required this.autoSuggestions,
    required this.mainPrompt,
    required this.expandPrompt,
    required this.expandButtonText,
    required this.primaryColor,
    required this.secondaryColor,
    required this.iconEmoji,
  });

  static List<Category> getAllCategories() {
    return [
      Category(
        key: 'story',
        name: 'قصة',
        icon: Icons.book,
        placeholder: 'اكتب فكرة قصة (مثال: مستكشف يجد مدينة مفقودة)...',
        suggestions: [
          'رائد فضاء وحيد',
          'جريمة في قطار فاخر',
          'مغامرة في عالم سحري',
          'قصة حب في زمن الحرب',
          'لغز في مكتبة قديمة'
        ],
        autoSuggestions: [
          'مغامرة في الفضاء',
          'قصة حب في المستقبل',
          'لغز في قصر قديم',
          'رحلة عبر الزمن',
          'صداقة بين إنسان وروبوت',
          'مدينة تحت الماء',
          'عالم سحري مخفي',
          'مغامرة في الغابة',
          'قصة بطل خارق',
          'رحلة إلى كوكب آخر',
          'حكاية أسطورية',
          'مغامرة في الصحراء'
        ],
        mainPrompt:
            '''أنت روائي محترف متمكن من فنون السرد العربي الحديث. اقترح 4 بدايات قصص مختلفة حول موضوع: [USER_INPUT]

لكل قصة قدم:
- عنوان جذاب ومشوق للقصة
- نوع القصة (مغامرة، رومانسية، خيال علمي، تشويق، دراما، كوميديا)
- بداية مشوقة للقصة (150-200 كلمة) تتضمن:
  * تقديم الشخصية الرئيسية
  * وصف المكان والزمان
  * حدث مثير يجذب القارئ
  * نهاية مفتوحة تدفع للمتابعة

معايير الكتابة:
- ابدأ بمشهد مثير أو حوار جذاب
- اجعل القارئ يتساءل "ماذا سيحدث بعد ذلك؟"
- استخدم تقنيات التشويق والإثارة
- لا تكشف الكثير من الأحداث
- اترك القارئ متشوقاً للمتابعة

يجب أن يكون الرد باللغة العربية حصراً.''',
        expandPrompt: '''أنت روائي محترف، أكمل القصة '[TITLE]' بالجزء التالي.

القصة حتى الآن: [EXISTING_TEXT]

اكتب الجزء التالي (200-300 كلمة) مع:
1. تطوير الأحداث بشكل طبيعي ومشوق
2. تعميق الشخصيات وإظهار مشاعرها
3. إضافة تفاصيل مثيرة وغير متوقعة
4. استخدام حوارات طبيعية ومعبرة
5. وصف الأماكن والأجواء بجمال
6. إنهاء الجزء بتشويق يدفع للمتابعة

ملاحظة مهمة:
- إذا كانت هذه المرة الثالثة أو أكثر لإكمال القصة، اكتب نهاية مؤثرة ومُرضية للقصة
- لا تكرر الأحداث السابقة
- حافظ على تسلسل منطقي للأحداث

اكتب باللغة العربية الفصحى مع تقسيم لفقرات منظمة.''',
        expandButtonText: 'أكمل القصة',
        primaryColor: const Color(0xFFec4899),
        secondaryColor: const Color(0xFFbe185d),
        iconEmoji: '📖',
      ),
      Category(
        key: 'kitchen',
        name: 'مطبخ',
        icon: Icons.restaurant,
        placeholder: 'اكتب المكونات المتوفرة لديك (مثال: دجاج، أرز)...',
        suggestions: [
          'لحم، بطاطس، بصل',
          'بيض، جبن، خبز',
          'معكرونة، صلصة طماطم',
          'أرز، خضار، دجاج',
          'سمك، ليمون، أعشاب'
        ],
        autoSuggestions: [
          'دجاج، أرز، خضار',
          'لحم، بطاطس، بصل',
          'سمك، ليمون، أعشاب',
          'بيض، جبن، طماطم',
          'معكرونة، صلصة، فطر',
          'أرز، حليب جوز الهند',
          'خضار مشكلة، توابل',
          'فراخ، عسل، صويا صوص',
          'لحم مفروم، باذنجان',
          'سمك سلمون، أفوكادو',
          'روبيان، ثوم، فلفل',
          'خضار ورقية، جوز'
        ],
        mainPrompt:
            '''أنت شيف عالمي خبير في فنون الطبخ والابتكار الغذائي. مهمتك ابتكار 4 وصفات متنوعة باستخدام المكونات: [USER_INPUT]

معايير الابتكار:
- امزج بين المطابخ العالمية والعربية
- اقترح تقنيات طبخ مختلفة (شوي، قلي، طبخ بطيء، إلخ)
- راعي التوازن الغذائي والطعم
- أضف لمسات إبداعية في التقديم
- اقترح بدائل للمكونات غير المتوفرة

يجب أن يكون الرد باللغة العربية حصراً.''',
        expandPrompt: '''قدم وصفة '[TITLE]' بالتنسيق التالي بالضبط:

## 🍽️ [TITLE]

### 📋 المكونات:
• [قائمة المكونات مع الكميات الدقيقة]

### 👨‍🍳 طريقة التحضير:
1. [خطوة أولى مفصلة]
2. [خطوة ثانية مفصلة]
3. [باقي الخطوات...]

### ⏰ معلومات الطبق:
• وقت التحضير: [الوقت]
• وقت الطبخ: [الوقت]
• عدد الأشخاص: [العدد]
• مستوى الصعوبة: [سهل/متوسط/صعب]

### 💡 نصائح الشيف:
• [نصائح مهمة لنجاح الطبق]

### 🎨 التقديم والتزيين:
• [طرق التقديم والتزيين]

### 🔄 بدائل المكونات:
• [بدائل للمكونات غير المتوفرة]

استخدم هذا التنسيق بالضبط مع الرموز والعناوين كما هي.''',
        expandButtonText: 'عرض الوصفة بالتفصيل',
        primaryColor: const Color(0xFFf97316),
        secondaryColor: const Color(0xFFea580c),
        iconEmoji: '🍳',
      ),
      Category(
        key: 'health',
        name: 'صحة',
        icon: Icons.health_and_safety,
        placeholder:
            'اكتب مشكلة صحية أو موضوع تريد معرفة طرق علاجه (مثال: الصداع، الأرق، آلام الظهر)...',
        suggestions: [
          'الصداع',
          'الأرق',
          'آلام الظهر',
          'نزلات البرد',
          'القلق',
          'الإجهاد',
          'آلام المعدة',
          'الحساسية'
        ],
        autoSuggestions: [
          'الصداع',
          'الأرق',
          'آلام الظهر',
          'نزلات البرد',
          'القلق',
          'الإجهاد',
          'آلام المعدة',
          'الحساسية',
          'ارتفاع ضغط الدم',
          'السكري',
          'الكوليسترول',
          'آلام المفاصل',
          'الاكتئاب',
          'حرقة المعدة',
          'الإمساك',
          'الصداع النصفي',
          'آلام الأسنان',
          'التهاب الحلق'
        ],
        mainPrompt:
            '''أنت طبيب استشاري خبير، اقترح 3 طرق لعلاج أو التعامل مع: [USER_INPUT]

الطرق المطلوبة بالضبط:
1. الطريقة الطبية: العلاج الطبي المعتمد والأدوية المناسبة
2. الطريقة المنزلية: العلاجات الطبيعية والوصفات المنزلية الآمنة
3. الطريقة الإضافية: نصائح وقائية أو علاجات بديلة مكملة

تأكد من:
- تقديم معلومات طبية دقيقة وآمنة
- التنبيه لضرورة استشارة الطبيب عند الحاجة
- ذكر أي تحذيرات أو احتياطات مهمة
- استخدام لغة واضحة ومفهومة

ملاحظة: هذه المعلومات للتثقيف الصحي ولا تغني عن استشارة الطبيب المختص.''',
        expandPrompt: '''قدم شرحاً طبياً مفصلاً لعلاج '[TITLE]' بالتنسيق التالي:

## 🏥 العلاج الطبي المفصل: [TITLE]

### 💊 العلاج الدوائي:
• [الأدوية المناسبة والجرعات]

### 🔬 التشخيص والفحوصات:
• [الفحوصات المطلوبة والتشخيص]

### 🏠 العلاج المنزلي:
• [الطرق الطبيعية والآمنة]

### ⚠️ التحذيرات والاحتياطات:
• [تحذيرات مهمة ومتى تستشير الطبيب]

### 🛡️ الوقاية:
• [طرق الوقاية من تكرار المشكلة]

### 📋 خطة العلاج:
1. [خطوة أولى]
2. [خطوة ثانية]
3. [باقي الخطوات...]

### 🔄 المتابعة:
• [كيفية متابعة التحسن والشفاء]

تذكر: هذه المعلومات للتثقيف الصحي فقط ولا تغني عن استشارة الطبيب المختص.''',
        expandButtonText: 'عرض التفاصيل الطبية',
        primaryColor: const Color(0xFF10b981),
        secondaryColor: const Color(0xFF059669),
        iconEmoji: '🏥',
      ),
      Category(
        key: 'educational',
        name: 'تعليم',
        icon: Icons.school,
        placeholder:
            'اكتب أي موضوع أو مادة تريد تعلمها (مثال: الرياضيات، الفيزياء، التاريخ، البرمجة)...',
        suggestions: [
          'الرياضيات',
          'الفيزياء',
          'الكيمياء',
          'التاريخ',
          'الجغرافيا',
          'البرمجة',
          'اللغة الإنجليزية',
          'الأحياء',
          'الاقتصاد',
          'الفلسفة'
        ],
        autoSuggestions: [
          'الرياضيات',
          'الفيزياء',
          'الكيمياء',
          'الأحياء',
          'التاريخ',
          'الجغرافيا',
          'اللغة العربية',
          'اللغة الإنجليزية',
          'البرمجة',
          'الاقتصاد',
          'الفلسفة',
          'علم النفس',
          'الجبر',
          'الهندسة',
          'التفاضل والتكامل',
          'الإحصاء',
          'نظرية فيثاغورس',
          'قوانين نيوتن'
        ],
        mainPrompt:
            '''أنت أستاذ خصوصي خبير ومتخصص في: [USER_INPUT]. مهمتك تقديم 4 طرق تعليمية مختلفة ومبتكرة لشرح هذا الموضوع.

كأستاذ خصوصي محترف:
- افهم مستوى الطالب واحتياجاته
- استخدم أساليب تعليمية متنوعة (بصرية، سمعية، عملية)
- اجعل التعلم ممتعاً وتفاعلياً
- استخدم أمثلة من الواقع والحياة اليومية
- قدم طرق مختلفة تناسب أنماط التعلم المختلفة
- اربط المعلومات الجديدة بما يعرفه الطالب مسبقاً

قدم كل طريقة بعنوان واضح ووصف مفصل لكيفية التطبيق.''',
        expandPrompt: '''أنت أستاذ خصوصي متخصص، اشرح '[TITLE]' بالتنسيق التالي:

## 🎓 شرح: [TITLE]

### 📚 المفهوم الأساسي:
• [شرح بسيط ومفهوم للموضوع]

### 🔍 التفاصيل المهمة:
• [النقاط الرئيسية والتفاصيل الضرورية]

### 💡 أمثلة من الحياة:
• [أمثلة واقعية يمكن للطالب فهمها بسهولة]

### 📝 خطوات التطبيق:
1. [خطوة أولى واضحة]
2. [خطوة ثانية مفصلة]
3. [باقي الخطوات...]

### 🎯 تمارين عملية:
• [تمارين وأنشطة للتطبيق]

### 🧠 نصائح للحفظ والفهم:
• [طرق تساعد على التذكر والفهم العميق]

### ❓ أسئلة للمراجعة:
• [أسئلة لاختبار الفهم]

### 📖 للتوسع أكثر:
• [مواضيع مرتبطة ومصادر إضافية]

استخدم لغة بسيطة وواضحة مناسبة لجميع المستويات.''',
        expandButtonText: 'عرض الشرح التفصيلي',
        primaryColor: const Color(0xFF3b82f6),
        secondaryColor: const Color(0xFF1d4ed8),
        iconEmoji: '🎓',
      ),
      Category(
        key: 'project',
        name: 'مشاريع',
        icon: Icons.business,
        placeholder:
            'اكتب فكرة أو مجال تريد الاستثمار فيه (مثال: مقهى، تطبيق، خدمة توصيل)...',
        suggestions: [
          'مقهى متخصص',
          'تطبيق جوال',
          'خدمة توصيل',
          'متجر إلكتروني',
          'مركز تدريب',
          'مشروع غذائي',
          'خدمة تنظيف',
          'ورشة حرفية'
        ],
        autoSuggestions: [
          'مقهى متخصص',
          'متجر إلكتروني',
          'خدمة توصيل طعام',
          'مركز تدريب مهني',
          'استوديو تصوير',
          'مشروع كوفي شوب',
          'خدمة تنظيف منازل',
          'ورشة حرف يدوية',
          'مشروع حلويات منزلية',
          'خدمة صيانة جوالات',
          'مركز لياقة نسائي',
          'مشروع زراعة مائية',
          'متجر إكسسوارات'
        ],
        mainPrompt:
            '''أنت مستشار استثماري خبير في المشاريع الصغيرة والمتوسطة. اقترح 4 أفكار مشاريع مربحة مبنية على: [USER_INPUT]

ركز على:
- مشاريع بسيطة وقابلة للتنفيذ
- رأس مال معقول (من 5,000 إلى 100,000 ريال)
- عائد استثماري واضح
- سوق محلي أو إقليمي
- إمكانية البدء من المنزل أو مساحة صغيرة

اجعل كل فكرة عملية ومباشرة مع تقدير أولي للتكلفة والربح المتوقع.''',
        expandPrompt:
            '''ضع خطة استثمارية مبسطة لمشروع '[TITLE]' بالتنسيق التالي:

## 💼 خطة مشروع: [TITLE]

### 💰 التكاليف والاستثمار:
• رأس المال المطلوب: [المبلغ]
• التكاليف الشهرية: [المبلغ]
• نقطة التعادل: [المدة]

### 📊 الأرباح المتوقعة:
• الإيرادات الشهرية المتوقعة: [المبلغ]
• هامش الربح: [النسبة]
• العائد على الاستثمار: [النسبة سنوياً]

### 🎯 الجمهور المستهدف:
• [وصف الفئة المستهدفة]

### 📈 استراتيجية التسويق:
• [طرق التسويق البسيطة والفعالة]

### ⚠️ المخاطر الرئيسية:
• [أهم 3 مخاطر وكيفية تجنبها]

### 🚀 خطوات البدء:
1. [الخطوة الأولى]
2. [الخطوة الثانية]
3. [الخطوة الثالثة]

استخدم أرقام واقعية وعملية للسوق السعودي/الخليجي.''',
        expandButtonText: 'عرض خطة العمل',
        primaryColor: const Color(0xFF059669),
        secondaryColor: const Color(0xFF047857),
        iconEmoji: '💼',
      ),
      Category(
        key: 'dealings',
        name: 'تعاملات',
        icon: Icons.chat,
        placeholder: 'اكتب موقفًا اجتماعيًا (مثال: كيفية بدء حوار)...',
        suggestions: [
          'الرد على نقد غير بناء',
          'طلب زيادة في الراتب',
          'الاعتذار بشكل فعال',
          'التعامل مع الصراعات',
          'بناء علاقات مهنية'
        ],
        autoSuggestions: [
          'كيفية بدء محادثة',
          'التعامل مع النقد',
          'طلب زيادة راتب',
          'الاعتذار بفعالية',
          'حل النزاعات',
          'بناء الثقة',
          'التفاوض الناجح',
          'إدارة الوقت',
          'التعامل مع الضغط',
          'بناء علاقات مهنية',
          'التواصل الفعال',
          'إدارة الفريق'
        ],
        mainPrompt:
            '''أنت خبير علم النفس الاجتماعي والتواصل الإنساني. قدم 5 استراتيجيات ذكية للتعامل مع: [USER_INPUT]

أسس التواصل الفعال:
- اطبق مبادئ علم النفس السلوكي
- استخدم تقنيات التواصل اللاعنفي
- راعي الذكاء العاطفي والاجتماعي
- اقترح حلولاً عملية وقابلة للتطبيق
- احترم الثقافة العربية والقيم الاجتماعية

يجب أن يكون الرد باللغة العربية حصراً.''',
        expandPrompt: '''اشرح استراتيجية '[TITLE]' بالتنسيق التالي:

## 🧠 شرح الاستراتيجية: [TITLE]

### 🔍 السبب النفسي والاجتماعي:
• [لماذا تعمل هذه الاستراتيجية من الناحية النفسية]

### 📋 خطوات التطبيق:
1. [الخطوة الأولى بالتفصيل]
2. [الخطوة الثانية بالتفصيل]
3. [الخطوة الثالثة بالتفصيل]

### 💬 مثال على الحوار:
**الموقف:** [وصف الموقف]
**أنت:** "[ما تقوله بالضبط]"
**الطرف الآخر:** "[رد محتمل]"
**أنت:** "[ردك التالي]"

### 🎯 العبارات المقترحة:
• "[عبارة محددة 1]"
• "[عبارة محددة 2]"
• "[عبارة محددة 3]"

### ⏰ التوقيت المناسب:
• [متى تستخدم هذه الاستراتيجية]

### ✅ النتائج المتوقعة:
• [الفوائد والنتائج الإيجابية]

### ❌ أخطاء يجب تجنبها:
• [الأخطاء الشائعة وكيفية تجنبها]

### 💡 نصائح للنجاح:
• [نصائح عملية لتطبيق ناجح]

استخدم أمثلة واقعية وحوارات كاملة باللغة العربية.''',
        expandButtonText: 'شرح الاستراتيجية',
        primaryColor: const Color(0xFF8b5cf6),
        secondaryColor: const Color(0xFF7c3aed),
        iconEmoji: '🤝',
      ),
    ];
  }
}
