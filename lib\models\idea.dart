import 'package:uuid/uuid.dart';

class Idea {
  final String id;
  final String title;
  final String description;
  final String? expandedContent;
  final bool isExpanded;
  final int clickCount;
  final DateTime createdAt;

  Idea({
    String? id,
    required this.title,
    required this.description,
    this.expandedContent,
    this.isExpanded = false,
    this.clickCount = 0,
    DateTime? createdAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now();

  Idea copyWith({
    String? id,
    String? title,
    String? description,
    String? expandedContent,
    bool? isExpanded,
    int? clickCount,
    DateTime? createdAt,
  }) {
    return Idea(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      expandedContent: expandedContent ?? this.expandedContent,
      isExpanded: isExpanded ?? this.isExpanded,
      clickCount: clickCount ?? this.clickCount,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'expandedContent': expandedContent,
      'isExpanded': isExpanded,
      'clickCount': clickCount,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory Idea.fromJson(Map<String, dynamic> json) {
    return Idea(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      expandedContent: json['expandedContent'],
      isExpanded: json['isExpanded'] ?? false,
      clickCount: json['clickCount'] ?? 0,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Idea && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Idea(id: $id, title: $title, description: $description, isExpanded: $isExpanded, clickCount: $clickCount)';
  }
}
