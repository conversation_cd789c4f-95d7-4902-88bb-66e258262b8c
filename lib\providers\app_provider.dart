import 'package:flutter/material.dart';
import '../models/category.dart';
import '../models/idea.dart';
import '../services/ai_service.dart';
import '../services/storage_service.dart';

class AppProvider extends ChangeNotifier {
  final AIService _aiService = AIService();
  final StorageService _storageService = StorageService();
  
  // Current state
  Category? _currentCategory;
  String _narratorStyle = 'narrator'; // for story category
  List<Idea> _currentIdeas = [];
  bool _isLoading = false;
  String _errorMessage = '';
  List<Map<String, dynamic>> _history = [];
  
  // Auto suggestions
  List<String> _autoSuggestions = [];
  int _currentSuggestionIndex = -1;
  
  // Getters
  Category? get currentCategory => _currentCategory;
  String get narratorStyle => _narratorStyle;
  List<Idea> get currentIdeas => _currentIdeas;
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;
  List<Map<String, dynamic>> get history => _history;
  List<String> get autoSuggestions => _autoSuggestions;
  int get currentSuggestionIndex => _currentSuggestionIndex;
  
  // Initialize the provider
  Future<void> initialize() async {
    await _storageService.initialize();
    await loadHistory();
    notifyListeners();
  }
  
  // Select category
  void selectCategory(Category category) {
    _currentCategory = category;
    _currentIdeas.clear();
    _errorMessage = '';
    _autoSuggestions.clear();
    _currentSuggestionIndex = -1;
    notifyListeners();
  }
  
  // Select narrator style (for story category)
  void selectNarratorStyle(String style) {
    _narratorStyle = style;
    notifyListeners();
  }
  
  // Generate ideas
  Future<void> generateIdeas(String topic) async {
    if (_currentCategory == null || topic.trim().isEmpty) {
      _errorMessage = 'الرجاء اختيار قسم وإدخال موضوع';
      notifyListeners();
      return;
    }
    
    _isLoading = true;
    _errorMessage = '';
    _currentIdeas.clear();
    notifyListeners();
    
    try {
      String prompt = _buildPrompt(topic);
      List<Idea> ideas = await _aiService.generateIdeas(prompt);
      
      _currentIdeas = ideas;
      
      // Save to history
      await _saveToHistory(topic, _currentCategory!.name, ideas);
      
    } catch (e) {
      _errorMessage = 'حدث خطأ: ${e.toString()}';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // Expand idea
  Future<void> expandIdea(Idea idea, String originalTopic) async {
    if (_currentCategory == null) return;
    
    try {
      String expandedContent = await _aiService.expandIdea(
        idea, 
        _currentCategory!, 
        originalTopic,
        _narratorStyle,
      );
      
      // Update the idea with expanded content
      int index = _currentIdeas.indexWhere((i) => i.id == idea.id);
      if (index != -1) {
        _currentIdeas[index] = _currentIdeas[index].copyWith(
          expandedContent: expandedContent,
          isExpanded: true,
        );
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'فشل في توسيع الفكرة: ${e.toString()}';
      notifyListeners();
    }
  }
  
  // Auto suggestions
  void showAutoSuggestions(String query) {
    if (_currentCategory == null || query.length < 2) {
      _autoSuggestions.clear();
      _currentSuggestionIndex = -1;
      notifyListeners();
      return;
    }
    
    List<String> suggestions = _currentCategory!.autoSuggestions
        .where((suggestion) =>
            suggestion.toLowerCase().contains(query.toLowerCase()) ||
            query.toLowerCase().contains(suggestion.toLowerCase()))
        .take(6)
        .toList();
    
    _autoSuggestions = suggestions;
    _currentSuggestionIndex = -1;
    notifyListeners();
  }
  
  void hideSuggestions() {
    _autoSuggestions.clear();
    _currentSuggestionIndex = -1;
    notifyListeners();
  }
  
  void updateSuggestionIndex(int index) {
    _currentSuggestionIndex = index;
    notifyListeners();
  }
  
  // History management
  Future<void> loadHistory() async {
    try {
      _history = await _storageService.getHistory();
      notifyListeners();
    } catch (e) {
      print('Error loading history: $e');
    }
  }
  
  Future<void> _saveToHistory(String topic, String categoryName, List<Idea> ideas) async {
    try {
      await _storageService.saveToHistory(topic, categoryName, ideas);
      await loadHistory(); // Refresh history
    } catch (e) {
      print('Error saving to history: $e');
    }
  }
  
  Future<void> clearHistory() async {
    try {
      await _storageService.clearHistory();
      _history.clear();
      notifyListeners();
    } catch (e) {
      print('Error clearing history: $e');
    }
  }
  
  void loadFromHistory(Map<String, dynamic> historyItem) {
    try {
      String topic = historyItem['topic'];
      String categoryName = historyItem['category'];
      List<dynamic> ideasJson = historyItem['ideas'];
      
      // Find matching category
      Category? category = Category.getAllCategories()
          .firstWhere((cat) => cat.name == categoryName);
      
      if (category != null) {
        _currentCategory = category;
        _currentIdeas = ideasJson.map((json) => Idea.fromJson(json)).toList();
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'خطأ في استرجاع العنصر من السجل';
      notifyListeners();
    }
  }
  
  // Build prompt based on category and narrative style
  String _buildPrompt(String topic) {
    String prompt = _currentCategory!.mainPrompt.replaceAll('[USER_INPUT]', topic);
    
    if (_currentCategory!.key == 'story') {
      String styleText = _narratorStyle == 'protagonist' 
          ? 'بطل القصة (منظور الشخص الأول)' 
          : 'الراوي (منظور الشخص الثالث)';
      prompt = prompt.replaceAll('[NARRATIVE_STYLE]', styleText);
    }
    
    return prompt;
  }
}
