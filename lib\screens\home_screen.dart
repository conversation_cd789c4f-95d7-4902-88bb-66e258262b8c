import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math' as math;
import '../services/ai_service.dart';
import '../models/category.dart';
import '../models/idea.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  final TextEditingController _textController = TextEditingController();
  final AIService _aiService = AIService();

  Category? _selectedCategory;
  List<Idea> _ideas = [];
  bool _isLoading = false;
  String _errorMessage = '';

  final List<Category> _categories = [
    Category(
      key: 'story',
      name: 'قصة',
      icon: Icons.book,
      emoji: '📖',
      placeholder: 'اكتب فكرة قصة (مثال: مستكشف يجد مدينة مفقودة)...',
      color: const Color(0xFFec4899),
    ),
    Category(
      key: 'kitchen',
      name: 'مطب<PERSON>',
      icon: Icons.restaurant,
      emoji: '🍳',
      placeholder: 'اكتب المكونات المتوفرة لديك (مثال: دجاج، أرز)...',
      color: const Color(0xFFf97316),
    ),
    Category(
      key: 'health',
      name: 'صحة',
      icon: Icons.health_and_safety,
      emoji: '🏥',
      placeholder: 'اكتب مشكلة صحية (مثال: الصداع، الأرق)...',
      color: const Color(0xFF10b981),
    ),
    Category(
      key: 'education',
      name: 'تعليم',
      icon: Icons.school,
      emoji: '🎓',
      placeholder: 'اكتب موضوع تريد تعلمه (مثال: الرياضيات، البرمجة)...',
      color: const Color(0xFF3b82f6),
    ),
    Category(
      key: 'projects',
      name: 'مشاريع',
      icon: Icons.business,
      emoji: '💼',
      placeholder: 'اكتب فكرة مشروع (مثال: مقهى، تطبيق)...',
      color: const Color(0xFF059669),
    ),
    Category(
      key: 'social',
      name: 'تعاملات',
      icon: Icons.chat,
      emoji: '🤝',
      placeholder: 'اكتب موقفاً اجتماعياً (مثال: كيفية بدء حوار)...',
      color: const Color(0xFF8b5cf6),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Animated background
          _buildAnimatedBackground(),

          // Main content
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const SizedBox(height: 20),

                  // App header
                  _buildHeader(),

                  const SizedBox(height: 30),

                  // Main content card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        children: [
                          // Category selector
                          _buildCategorySelector(),

                          const SizedBox(height: 24),

                          // Input section
                          _buildInputSection(),

                          const SizedBox(height: 24),

                          // Results section
                          _buildResultsSection(),
                        ],
                      ),
                    ),
                  ).animate().fadeIn(duration: 300.ms, delay: 300.ms),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1a1a2e),
            Color(0xFF16213e),
            Color(0xFF0d1117),
          ],
        ),
      ),
      child: AnimatedBuilder(
        animation: _backgroundController,
        builder: (context, child) {
          return Stack(
            children: [
              Positioned(
                top: -100 +
                    (200 * math.sin(_backgroundController.value * 2 * math.pi)),
                left: -100 +
                    (200 * math.cos(_backgroundController.value * 2 * math.pi)),
                child: Container(
                  width: 300,
                  height: 300,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        const Color(0xFF93c5fd).withValues(alpha: 0.15),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: -200 +
                    (100 *
                        math.sin(
                            _backgroundController.value * 2 * math.pi + 2)),
                right: -150 +
                    (300 *
                        math.cos(
                            _backgroundController.value * 2 * math.pi + 1)),
                child: Container(
                  width: 350,
                  height: 350,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        const Color(0xFFc4b5fd).withValues(alpha: 0.12),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // App icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: const LinearGradient(
              colors: [Color(0xFFc039ff), Color(0xFF6a11cb)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFc039ff).withValues(alpha: 0.3),
                blurRadius: 20,
                spreadRadius: 2,
              ),
            ],
          ),
          child: const Icon(
            Icons.psychology,
            size: 40,
            color: Colors.white,
          ),
        )
            .animate(onPlay: (controller) => controller.repeat())
            .moveY(
                begin: 0, end: -5, duration: 3000.ms, curve: Curves.easeInOut)
            .then()
            .moveY(
                begin: -5, end: 0, duration: 3000.ms, curve: Curves.easeInOut),

        const SizedBox(height: 16),

        // App title
        ShaderMask(
          shaderCallback: (bounds) => const LinearGradient(
            colors: [
              Color(0xFFa78bfa),
              Color(0xFFec4899),
              Color(0xFF6366f1),
            ],
          ).createShader(bounds),
          child: const Text(
            'العقل المبدع',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ).animate().fadeIn(duration: 600.ms, delay: 100.ms),

        const SizedBox(height: 8),

        // Subtitle
        Text(
          _selectedCategory != null
              ? 'اختر موضوعاً وأطلق العنان لإبداعك'
              : 'اختر قسماً وأطلق العنان لإبداعك',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[300],
              ),
          textAlign: TextAlign.center,
        ).animate().fadeIn(duration: 600.ms, delay: 200.ms),
      ],
    );
  }

  Widget _buildCategorySelector() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.0,
      ),
      itemCount: _categories.length,
      itemBuilder: (context, index) {
        final category = _categories[index];
        final isSelected = _selectedCategory?.key == category.key;

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedCategory = category;
              _ideas.clear();
              _errorMessage = '';
            });
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            decoration: BoxDecoration(
              color: isSelected
                  ? category.color.withValues(alpha: 0.2)
                  : Colors.white.withValues(alpha: 0.02),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected
                    ? category.color.withValues(alpha: 0.5)
                    : Colors.white.withValues(alpha: 0.1),
                width: isSelected ? 2 : 1,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: category.color.withValues(alpha: 0.3),
                        blurRadius: 12,
                        spreadRadius: 2,
                      ),
                    ]
                  : [],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? category.color.withValues(alpha: 0.2)
                        : Colors.grey[800]?.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Text(
                      category.emoji,
                      style: const TextStyle(fontSize: 24),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  category.name,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: isSelected ? Colors.white : Colors.grey[300],
                        fontWeight:
                            isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        )
            .animate()
            .fadeIn(
              duration: 300.ms,
              delay: Duration(milliseconds: index * 50),
            )
            .scale(begin: const Offset(0.8, 0.8), end: const Offset(1.0, 1.0));
      },
    );
  }

  Widget _buildInputSection() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _textController,
                enabled: _selectedCategory != null,
                decoration: InputDecoration(
                  hintText:
                      _selectedCategory?.placeholder ?? 'اختر قسماً أولاً...',
                  suffixIcon: _textController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _textController.clear();
                            setState(() {});
                          },
                        )
                      : null,
                ),
                onChanged: (value) => setState(() {}),
                onSubmitted: (value) {
                  if (value.trim().isNotEmpty) {
                    _generateIdeas(value.trim());
                  }
                },
              ),
            ),
            const SizedBox(width: 12),
            ElevatedButton(
              onPressed: _selectedCategory != null &&
                      _textController.text.trim().isNotEmpty &&
                      !_isLoading
                  ? () => _generateIdeas(_textController.text.trim())
                  : null,
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('ألهمني'),
            ),
          ],
        ),
        if (_errorMessage.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 16),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _errorMessage,
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
              ],
            ),
          ).animate().fadeIn(duration: 300.ms).shake(),
      ],
    );
  }

  Widget _buildResultsSection() {
    if (_isLoading) {
      return Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                gradient: const LinearGradient(
                  colors: [Color(0xFFc039ff), Color(0xFF6a11cb)],
                ),
              ),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 3,
                ),
              ),
            )
                .animate(onPlay: (controller) => controller.repeat())
                .rotate(duration: 1000.ms),
            const SizedBox(height: 16),
            Text(
              'العقل المبدع يفكر...',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 16,
                letterSpacing: 1.5,
              ),
            )
                .animate(onPlay: (controller) => controller.repeat())
                .fadeIn(duration: 1000.ms)
                .then()
                .fadeOut(duration: 1000.ms),
          ],
        ),
      );
    }

    if (_ideas.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.lightbulb_outline,
              size: 48,
              color: Colors.grey[600],
            ),
            const SizedBox(height: 16),
            Text(
              'الأفكار التي سيتم إنشاؤها ستظهر هنا.',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ).animate().fadeIn(duration: 500.ms, delay: 500.ms);
    }

    return Column(
      children: _ideas.asMap().entries.map((entry) {
        final index = entry.key;
        final idea = entry.value;

        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        _selectedCategory?.emoji ?? '💡',
                        style: const TextStyle(fontSize: 24),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          idea.title,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            foreground: Paint()
                              ..shader = LinearGradient(
                                colors: [
                                  _selectedCategory?.color ??
                                      const Color(0xFFc039ff),
                                  const Color(0xFF6a11cb)
                                ],
                              ).createShader(
                                  const Rect.fromLTWH(0, 0, 200, 70)),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[800]?.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(8),
                      border: Border(
                        right: BorderSide(
                          color: (_selectedCategory?.color ??
                                  const Color(0xFFc039ff))
                              .withValues(alpha: 0.5),
                          width: 3,
                        ),
                      ),
                    ),
                    child: Text(
                      idea.description,
                      style: TextStyle(
                        color: Colors.grey[300],
                        height: 1.6,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        )
            .animate()
            .fadeIn(
              duration: 400.ms,
              delay: Duration(milliseconds: index * 100),
            )
            .slideY(begin: 0.3, end: 0);
      }).toList(),
    );
  }

  Future<void> _generateIdeas(String topic) async {
    if (_selectedCategory == null || topic.trim().isEmpty) {
      setState(() {
        _errorMessage = 'الرجاء اختيار قسم وإدخال موضوع';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _ideas.clear();
    });

    try {
      final ideas = await _aiService.generateIdeas(_selectedCategory!, topic);
      setState(() {
        _ideas = ideas;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
