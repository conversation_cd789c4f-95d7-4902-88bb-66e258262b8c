import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../providers/app_provider.dart';
import '../models/category.dart';
import '../widgets/animated_background.dart';
import '../widgets/category_selector.dart';
import '../widgets/input_section.dart';
import '../widgets/results_section.dart';
import '../widgets/history_button.dart';
import '../widgets/preloader.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    await Future.delayed(const Duration(seconds: 4)); // Preloader duration
    await context.read<AppProvider>().initialize();
    
    // Select first category by default
    final categories = Category.getAllCategories();
    if (categories.isNotEmpty) {
      context.read<AppProvider>().selectCategory(categories.first);
    }
    
    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Preloader();
    }

    return Scaffold(
      body: Stack(
        children: [
          // Animated background
          const AnimatedBackground(),
          
          // Main content
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Header with history button
                  Row(
                    children: [
                      const HistoryButton(),
                      const Spacer(),
                    ],
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // App header
                  _buildHeader(),
                  
                  const SizedBox(height: 30),
                  
                  // Main content card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        children: [
                          // Category selector
                          const CategorySelector(),
                          
                          const SizedBox(height: 24),
                          
                          // Input section
                          const InputSection(),
                          
                          const SizedBox(height: 24),
                          
                          // Results section
                          const ResultsSection(),
                        ],
                      ),
                    ),
                  ).animate().fadeIn(duration: 300.ms, delay: 300.ms),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // App icon with floating animation
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: const LinearGradient(
              colors: [Color(0xFFc039ff), Color(0xFF6a11cb)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFc039ff).withOpacity(0.3),
                blurRadius: 20,
                spreadRadius: 2,
              ),
            ],
          ),
          child: const Icon(
            Icons.psychology,
            size: 40,
            color: Colors.white,
          ),
        )
            .animate(onPlay: (controller) => controller.repeat())
            .moveY(begin: 0, end: -5, duration: 3000.ms, curve: Curves.easeInOut)
            .then()
            .moveY(begin: -5, end: 0, duration: 3000.ms, curve: Curves.easeInOut),
        
        const SizedBox(height: 16),
        
        // App title
        ShaderMask(
          shaderCallback: (bounds) => const LinearGradient(
            colors: [
              Color(0xFFa78bfa),
              Color(0xFFec4899),
              Color(0xFF6366f1),
            ],
          ).createShader(bounds),
          child: const Text(
            'العقل المبدع',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ).animate().fadeIn(duration: 600.ms, delay: 100.ms),
        
        const SizedBox(height: 8),
        
        // Subtitle
        Consumer<AppProvider>(
          builder: (context, provider, child) {
            String subtitle = provider.currentCategory != null
                ? 'اختر موضوعاً وأطلق العنان لإبداعك'
                : 'اختر قسماً وأطلق العنان لإبداعك';
            
            return Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[300],
              ),
              textAlign: TextAlign.center,
            );
          },
        ).animate().fadeIn(duration: 600.ms, delay: 200.ms),
      ],
    );
  }
}
