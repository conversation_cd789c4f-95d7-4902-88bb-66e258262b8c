import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/idea.dart';
import '../models/category.dart';

class AIService {
  static const String _apiKey = 'AIzaSyA8icSGnn9lL1T4_F__nJUdEdVMc5MT958';
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';

  Future<List<Idea>> generateIdeas(Category category, String topic) async {
    try {
      String prompt = _buildPrompt(category, topic);
      
      final response = await http.post(
        Uri.parse('$_baseUrl?key=$_apiKey'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'contents': [
            {
              'role': 'user',
              'parts': [
                {'text': prompt}
              ]
            }
          ],
          'generationConfig': {
            'responseMimeType': 'application/json',
            'responseSchema': {
              'type': 'OBJECT',
              'properties': {
                'ideas': {
                  'type': 'ARRAY',
                  'items': {
                    'type': 'OBJECT',
                    'properties': {
                      'title': {'type': 'STRING'},
                      'description': {'type': 'STRING'}
                    },
                    'required': ['title', 'description']
                  }
                }
              },
              'required': ['ideas']
            }
          }
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['candidates'] != null && 
            data['candidates'].isNotEmpty &&
            data['candidates'][0]['content'] != null &&
            data['candidates'][0]['content']['parts'] != null &&
            data['candidates'][0]['content']['parts'].isNotEmpty) {
          
          final textResponse = data['candidates'][0]['content']['parts'][0]['text'];
          final jsonResponse = json.decode(textResponse);
          
          if (jsonResponse['ideas'] != null) {
            final List<dynamic> ideasJson = jsonResponse['ideas'];
            return ideasJson.map((ideaJson) => Idea(
              title: ideaJson['title'] ?? 'فكرة بدون عنوان',
              description: ideaJson['description'] ?? 'وصف غير متوفر',
            )).toList();
          }
        }
      }
      
      // Fallback: return demo ideas if API fails
      return _getDemoIdeas(category);
    } catch (e) {
      // Return demo ideas if there's any error
      return _getDemoIdeas(category);
    }
  }

  String _buildPrompt(Category category, String topic) {
    switch (category.key) {
      case 'story':
        return '''أنت روائي محترف. اقترح 4 بدايات قصص مختلفة حول موضوع: $topic

لكل قصة قدم:
- عنوان جذاب ومشوق
- بداية مشوقة للقصة (100-150 كلمة)

يجب أن يكون الرد باللغة العربية حصراً.''';
      
      case 'kitchen':
        return '''أنت شيف عالمي خبير. ابتكر 4 وصفات متنوعة باستخدام المكونات: $topic

لكل وصفة قدم:
- اسم الطبق
- وصف مختصر للطبق وطريقة التحضير

يجب أن يكون الرد باللغة العربية حصراً.''';
      
      case 'health':
        return '''أنت طبيب استشاري خبير. اقترح 3 طرق لعلاج أو التعامل مع: $topic

الطرق المطلوبة:
1. الطريقة الطبية
2. الطريقة المنزلية
3. الطريقة الوقائية

يجب أن يكون الرد باللغة العربية حصراً.''';
      
      case 'education':
        return '''أنت أستاذ خصوصي خبير في: $topic. قدم 4 طرق تعليمية مختلفة ومبتكرة لشرح هذا الموضوع.

لكل طريقة قدم:
- عنوان الطريقة
- شرح مفصل لكيفية التطبيق

يجب أن يكون الرد باللغة العربية حصراً.''';
      
      case 'projects':
        return '''أنت مستشار استثماري خبير. اقترح 4 أفكار مشاريع مربحة مبنية على: $topic

لكل مشروع قدم:
- اسم المشروع
- وصف الفكرة والتنفيذ

يجب أن يكون الرد باللغة العربية حصراً.''';
      
      case 'social':
        return '''أنت خبير علم النفس الاجتماعي. قدم 4 استراتيجيات ذكية للتعامل مع: $topic

لكل استراتيجية قدم:
- عنوان الاستراتيجية
- شرح مفصل لكيفية التطبيق

يجب أن يكون الرد باللغة العربية حصراً.''';
      
      default:
        return '''اقترح 4 أفكار إبداعية حول موضوع: $topic

لكل فكرة قدم:
- عنوان الفكرة
- وصف مفصل للفكرة

يجب أن يكون الرد باللغة العربية حصراً.''';
    }
  }

  List<Idea> _getDemoIdeas(Category category) {
    switch (category.key) {
      case 'story':
        return [
          const Idea(
            title: 'المدينة المفقودة',
            description: 'مستكشف شاب يكتشف خريطة قديمة تقوده إلى مدينة مفقودة مليئة بالأسرار والكنوز، لكنه يواجه مخاطر غامضة في رحلته.',
          ),
          const Idea(
            title: 'رسالة من المستقبل',
            description: 'فتاة تتلقى رسائل غامضة من نفسها في المستقبل تحذرها من أحداث ستقع، وعليها أن تقرر ما إذا كانت ستصدق هذه الرسائل أم لا.',
          ),
        ];
      
      case 'kitchen':
        return [
          const Idea(
            title: 'أرز بالدجاج والخضار',
            description: 'طبق شهي ومغذي يجمع بين الأرز المطبوخ مع قطع الدجاج الطرية والخضار الملونة، متبل بالتوابل العربية الأصيلة.',
          ),
          const Idea(
            title: 'سلطة الدجاج المشوي',
            description: 'سلطة صحية ولذيذة تحتوي على قطع الدجاج المشوي مع الخضار الطازجة والصوص الخاص، مثالية للوجبات الخفيفة.',
          ),
        ];
      
      case 'health':
        return [
          const Idea(
            title: 'العلاج الطبي للصداع',
            description: 'استخدام المسكنات المناسبة مثل الباراسيتامول أو الإيبوبروفين حسب شدة الألم، مع استشارة الطبيب في الحالات المزمنة.',
          ),
          const Idea(
            title: 'العلاج المنزلي للصداع',
            description: 'تطبيق كمادات باردة على الجبهة، شرب كمية كافية من الماء، والراحة في مكان هادئ ومظلم لتخفيف الألم.',
          ),
        ];
      
      case 'education':
        return [
          const Idea(
            title: 'التعلم التفاعلي',
            description: 'استخدام الألعاب التعليمية والأنشطة التفاعلية لجعل عملية التعلم ممتعة وفعالة، مما يساعد على فهم المفاهيم بشكل أفضل.',
          ),
          const Idea(
            title: 'التعلم البصري',
            description: 'استخدام الرسوم البيانية والخرائط الذهنية والصور التوضيحية لتبسيط المعلومات المعقدة وتسهيل عملية الحفظ والفهم.',
          ),
        ];
      
      case 'projects':
        return [
          const Idea(
            title: 'مقهى متخصص',
            description: 'فتح مقهى يقدم أنواع قهوة مختارة مع أجواء مريحة للعمل والدراسة، يمكن إضافة خدمات إضافية مثل الكتب أو الألعاب.',
          ),
          const Idea(
            title: 'خدمة توصيل طعام صحي',
            description: 'تطوير خدمة توصيل وجبات صحية ومتوازنة للأشخاص المشغولين، مع التركيز على المكونات الطبيعية والطازجة.',
          ),
        ];
      
      case 'social':
        return [
          const Idea(
            title: 'بدء المحادثة بسؤال مفتوح',
            description: 'استخدم أسئلة مفتوحة تتطلب إجابات مفصلة بدلاً من نعم أو لا، مثل "ما رأيك في..." أو "كيف تشعر حيال...".',
          ),
          const Idea(
            title: 'الاستماع النشط',
            description: 'أظهر اهتماماً حقيقياً بما يقوله الآخرون من خلال التواصل البصري وطرح أسئلة متابعة وتلخيص ما سمعته.',
          ),
        ];
      
      default:
        return [
          const Idea(
            title: 'فكرة إبداعية',
            description: 'هذه فكرة تجريبية لاختبار التطبيق. يمكنك تجربة أقسام مختلفة للحصول على أفكار متنوعة.',
          ),
        ];
    }
  }
}
