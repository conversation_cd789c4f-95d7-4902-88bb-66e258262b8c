import 'package:dio/dio.dart';
import 'dart:convert';
import '../models/idea.dart';
import '../models/category.dart';

class AIService {
  static const String _apiKey = 'AIzaSyA8icSGnn9lL1T4_F__nJUdEdVMc5MT958';
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
  
  final Dio _dio = Dio();

  AIService() {
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 60);
  }

  Future<List<Idea>> generateIdeas(String prompt) async {
    try {
      final response = await _dio.post(
        '$_baseUrl?key=$_apiKey',
        data: {
          'contents': [
            {
              'role': 'user',
              'parts': [
                {'text': prompt}
              ]
            }
          ],
          'generationConfig': {
            'responseMimeType': 'application/json',
            'responseSchema': {
              'type': 'OBJECT',
              'properties': {
                'ideas': {
                  'type': 'ARRAY',
                  'items': {
                    'type': 'OBJECT',
                    'properties': {
                      'title': {'type': 'STRING'},
                      'description': {'type': 'STRING'}
                    },
                    'required': ['title', 'description']
                  }
                }
              },
              'required': ['ideas']
            }
          }
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['candidates'] != null && 
            data['candidates'].isNotEmpty &&
            data['candidates'][0]['content'] != null &&
            data['candidates'][0]['content']['parts'] != null &&
            data['candidates'][0]['content']['parts'].isNotEmpty) {
          
          final textResponse = data['candidates'][0]['content']['parts'][0]['text'];
          final jsonResponse = json.decode(textResponse);
          
          if (jsonResponse['ideas'] != null) {
            final List<dynamic> ideasJson = jsonResponse['ideas'];
            return ideasJson.map((ideaJson) => Idea(
              title: ideaJson['title'] ?? 'فكرة بدون عنوان',
              description: ideaJson['description'] ?? 'وصف غير متوفر',
            )).toList();
          }
        }
      }
      
      throw Exception('لم يتم العثور على محتوى في استجابة الـ API');
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout) {
        throw Exception('انتهت مهلة الاتصال. تحقق من اتصال الإنترنت');
      } else if (e.type == DioExceptionType.receiveTimeout) {
        throw Exception('انتهت مهلة استقبال البيانات');
      } else if (e.response?.statusCode == 429) {
        throw Exception('تم تجاوز حد الطلبات. حاول مرة أخرى لاحقاً');
      } else if (e.response?.statusCode == 403) {
        throw Exception('مفتاح API غير صحيح أو منتهي الصلاحية');
      } else {
        throw Exception('خطأ في الشبكة: ${e.message}');
      }
    } catch (e) {
      throw Exception('حدث خطأ غير متوقع: $e');
    }
  }

  Future<String> expandIdea(
    Idea idea, 
    Category category, 
    String originalTopic,
    String narratorStyle,
  ) async {
    try {
      String prompt = category.expandPrompt
          .replaceAll('[TITLE]', idea.title)
          .replaceAll('[EXISTING_TEXT]', idea.description);

      // Special handling for story category
      if (category.key == 'story') {
        int clickCount = idea.clickCount + 1;
        
        if (clickCount >= 5) {
          return 'انتهت القصة';
        }
        
        // Add context based on click count
        if (clickCount == 1) {
          prompt += '\n\nهذا هو الجزء الأول من القصة. طور الأحداث وأضف تفاصيل مشوقة.';
        } else if (clickCount == 2) {
          prompt += '\n\nهذا هو الجزء الثاني. اجعل الأحداث أكثر إثارة وتعقيداً.';
        } else if (clickCount == 3) {
          prompt += '\n\nهذا هو الجزء الثالث. اصل للذروة والتشويق الأكبر.';
        } else if (clickCount == 4) {
          prompt += '\n\nهذا هو الجزء الرابع. ابدأ في حل العقدة وتوضيح الأمور.';
        } else if (clickCount == 5) {
          prompt += '\n\nهذا هو الجزء الأخير. اكتب نهاية مؤثرة ومُرضية للقصة.';
        }
      }

      final response = await _dio.post(
        '$_baseUrl?key=$_apiKey',
        data: {
          'contents': [
            {
              'role': 'user',
              'parts': [
                {'text': prompt}
              ]
            }
          ],
          'generationConfig': {
            'responseMimeType': 'text/plain'
          }
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['candidates'] != null && 
            data['candidates'].isNotEmpty &&
            data['candidates'][0]['content'] != null &&
            data['candidates'][0]['content']['parts'] != null &&
            data['candidates'][0]['content']['parts'].isNotEmpty) {
          
          return data['candidates'][0]['content']['parts'][0]['text'];
        }
      }
      
      throw Exception('لم يتم العثور على محتوى في استجابة الـ API');
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout) {
        throw Exception('انتهت مهلة الاتصال');
      } else if (e.type == DioExceptionType.receiveTimeout) {
        throw Exception('انتهت مهلة استقبال البيانات');
      } else {
        throw Exception('خطأ في الشبكة: ${e.message}');
      }
    } catch (e) {
      throw Exception('حدث خطأ غير متوقع: $e');
    }
  }
}
