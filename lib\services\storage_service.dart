import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'dart:convert';
import '../models/idea.dart';

class StorageService {
  static Database? _database;
  static SharedPreferences? _prefs;

  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    _database = await _initDatabase();
    
    // Clean old history entries (older than 7 days)
    await _cleanOldHistory();
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'creative_mind.db');
    
    return await openDatabase(
      path,
      version: 1,
      onCreate: (db, version) async {
        await db.execute('''
          CREATE TABLE history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            topic TEXT NOT NULL,
            category TEXT NOT NULL,
            ideas TEXT NOT NULL,
            created_at TEXT NOT NULL
          )
        ''');
      },
    );
  }

  Future<void> saveToHistory(String topic, String categoryName, List<Idea> ideas) async {
    if (_database == null) return;

    try {
      final ideasJson = ideas.map((idea) => idea.toJson()).toList();
      
      await _database!.insert(
        'history',
        {
          'topic': topic,
          'category': categoryName,
          'ideas': json.encode(ideasJson),
          'created_at': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Error saving to history: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getHistory() async {
    if (_database == null) return [];

    try {
      final List<Map<String, dynamic>> maps = await _database!.query(
        'history',
        orderBy: 'created_at DESC',
        limit: 50, // Limit to last 50 entries
      );

      return maps.map((map) {
        return {
          'id': map['id'],
          'topic': map['topic'],
          'category': map['category'],
          'ideas': json.decode(map['ideas']),
          'createdAt': map['created_at'],
        };
      }).toList();
    } catch (e) {
      print('Error getting history: $e');
      return [];
    }
  }

  Future<void> clearHistory() async {
    if (_database == null) return;

    try {
      await _database!.delete('history');
    } catch (e) {
      print('Error clearing history: $e');
    }
  }

  Future<void> _cleanOldHistory() async {
    if (_database == null) return;

    try {
      final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
      
      await _database!.delete(
        'history',
        where: 'created_at < ?',
        whereArgs: [sevenDaysAgo.toIso8601String()],
      );
    } catch (e) {
      print('Error cleaning old history: $e');
    }
  }

  // Settings management
  Future<void> saveSetting(String key, dynamic value) async {
    if (_prefs == null) return;

    try {
      if (value is String) {
        await _prefs!.setString(key, value);
      } else if (value is int) {
        await _prefs!.setInt(key, value);
      } else if (value is bool) {
        await _prefs!.setBool(key, value);
      } else if (value is double) {
        await _prefs!.setDouble(key, value);
      } else if (value is List<String>) {
        await _prefs!.setStringList(key, value);
      }
    } catch (e) {
      print('Error saving setting: $e');
    }
  }

  T? getSetting<T>(String key, {T? defaultValue}) {
    if (_prefs == null) return defaultValue;

    try {
      final value = _prefs!.get(key);
      if (value is T) {
        return value;
      }
      return defaultValue;
    } catch (e) {
      print('Error getting setting: $e');
      return defaultValue;
    }
  }

  Future<void> removeSetting(String key) async {
    if (_prefs == null) return;

    try {
      await _prefs!.remove(key);
    } catch (e) {
      print('Error removing setting: $e');
    }
  }

  Future<void> clearAllSettings() async {
    if (_prefs == null) return;

    try {
      await _prefs!.clear();
    } catch (e) {
      print('Error clearing settings: $e');
    }
  }

  // App state management
  Future<void> saveLastCategory(String categoryKey) async {
    await saveSetting('last_category', categoryKey);
  }

  String? getLastCategory() {
    return getSetting<String>('last_category');
  }

  Future<void> saveNarratorStyle(String style) async {
    await saveSetting('narrator_style', style);
  }

  String getNarratorStyle() {
    return getSetting<String>('narrator_style', defaultValue: 'narrator') ?? 'narrator';
  }

  // App usage statistics
  Future<void> incrementUsageCount() async {
    final currentCount = getSetting<int>('usage_count', defaultValue: 0) ?? 0;
    await saveSetting('usage_count', currentCount + 1);
  }

  int getUsageCount() {
    return getSetting<int>('usage_count', defaultValue: 0) ?? 0;
  }

  Future<void> saveLastUsedDate() async {
    await saveSetting('last_used_date', DateTime.now().toIso8601String());
  }

  DateTime? getLastUsedDate() {
    final dateString = getSetting<String>('last_used_date');
    if (dateString != null) {
      try {
        return DateTime.parse(dateString);
      } catch (e) {
        return null;
      }
    }
    return null;
  }
}
