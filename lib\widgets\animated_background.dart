import 'package:flutter/material.dart';
import 'dart:math' as math;

class AnimatedBackground extends StatefulWidget {
  const AnimatedBackground({super.key});

  @override
  State<AnimatedBackground> createState() => _AnimatedBackgroundState();
}

class _AnimatedBackgroundState extends State<AnimatedBackground>
    with TickerProviderStateMixin {
  late AnimationController _controller1;
  late AnimationController _controller2;
  late AnimationController _controller3;

  @override
  void initState() {
    super.initState();
    
    _controller1 = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();
    
    _controller2 = AnimationController(
      duration: const Duration(seconds: 25),
      vsync: this,
    )..repeat();
    
    _controller3 = AnimationController(
      duration: const Duration(seconds: 30),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller1.dispose();
    _controller2.dispose();
    _controller3.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1a1a2e),
            Color(0xFF16213e),
            Color(0xFF0d1117),
          ],
        ),
      ),
      child: Stack(
        children: [
          // First animated gradient
          AnimatedBuilder(
            animation: _controller1,
            builder: (context, child) {
              return Positioned(
                top: -100 + (200 * math.sin(_controller1.value * 2 * math.pi)),
                left: -100 + (200 * math.cos(_controller1.value * 2 * math.pi)),
                child: Container(
                  width: 300,
                  height: 300,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        const Color(0xFF93c5fd).withOpacity(0.15),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
          
          // Second animated gradient
          AnimatedBuilder(
            animation: _controller2,
            builder: (context, child) {
              return Positioned(
                top: MediaQuery.of(context).size.height * 0.3 + 
                     (150 * math.sin(_controller2.value * 2 * math.pi + 1)),
                right: -150 + (300 * math.cos(_controller2.value * 2 * math.pi + 1)),
                child: Container(
                  width: 400,
                  height: 400,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        const Color(0xFFc4b5fd).withOpacity(0.12),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
          
          // Third animated gradient
          AnimatedBuilder(
            animation: _controller3,
            builder: (context, child) {
              return Positioned(
                bottom: -200 + (100 * math.sin(_controller3.value * 2 * math.pi + 2)),
                left: MediaQuery.of(context).size.width * 0.2 + 
                      (100 * math.cos(_controller3.value * 2 * math.pi + 2)),
                child: Container(
                  width: 350,
                  height: 350,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        const Color(0xFFfcd34d).withOpacity(0.08),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
          
          // Fourth animated gradient
          AnimatedBuilder(
            animation: _controller1,
            builder: (context, child) {
              return Positioned(
                top: MediaQuery.of(context).size.height * 0.6 + 
                     (120 * math.sin(_controller1.value * 2 * math.pi + 3)),
                right: MediaQuery.of(context).size.width * 0.1 + 
                       (80 * math.cos(_controller1.value * 2 * math.pi + 3)),
                child: Container(
                  width: 250,
                  height: 250,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        const Color(0xFFa7f3d0).withOpacity(0.1),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
          
          // Fifth animated gradient
          AnimatedBuilder(
            animation: _controller2,
            builder: (context, child) {
              return Positioned(
                top: MediaQuery.of(context).size.height * 0.1 + 
                     (80 * math.sin(_controller2.value * 2 * math.pi + 4)),
                left: MediaQuery.of(context).size.width * 0.7 + 
                      (60 * math.cos(_controller2.value * 2 * math.pi + 4)),
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        const Color(0xFFfbbf24).withOpacity(0.06),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
