import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../providers/app_provider.dart';
import '../models/category.dart';

class CategorySelector extends StatelessWidget {
  const CategorySelector({super.key});

  @override
  Widget build(BuildContext context) {
    final categories = Category.getAllCategories();
    
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        return Column(
          children: [
            // Category buttons grid
            LayoutBuilder(
              builder: (context, constraints) {
                // Responsive grid based on screen width
                int crossAxisCount = constraints.maxWidth > 600 ? 3 : 2;
                double childAspectRatio = constraints.maxWidth > 600 ? 1.2 : 1.0;
                
                return GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: crossAxisCount,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                    childAspectRatio: childAspectRatio,
                  ),
                  itemCount: categories.length,
                  itemBuilder: (context, index) {
                    final category = categories[index];
                    final isSelected = provider.currentCategory?.key == category.key;
                    
                    return _CategoryButton(
                      category: category,
                      isSelected: isSelected,
                      onTap: () => provider.selectCategory(category),
                      animationDelay: index * 50,
                    );
                  },
                );
              },
            ),
            
            // Story narrator options (only show for story category)
            if (provider.currentCategory?.key == 'story') ...[
              const SizedBox(height: 20),
              _buildNarratorSelector(context, provider),
            ],
          ],
        );
      },
    );
  }

  Widget _buildNarratorSelector(BuildContext context, AppProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[800]?.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey[700]!.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          Text(
            'اختر منظور السرد:',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[300],
            ),
          ),
          const SizedBox(height: 12),
          Container(
            decoration: BoxDecoration(
              color: Colors.grey[800]?.withOpacity(0.5),
              borderRadius: BorderRadius.circular(25),
            ),
            child: Row(
              children: [
                Expanded(
                  child: _NarratorButton(
                    text: 'بطل القصة',
                    isSelected: provider.narratorStyle == 'protagonist',
                    onTap: () => provider.selectNarratorStyle('protagonist'),
                  ),
                ),
                Expanded(
                  child: _NarratorButton(
                    text: 'الراوي',
                    isSelected: provider.narratorStyle == 'narrator',
                    onTap: () => provider.selectNarratorStyle('narrator'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 300.ms).slideY(begin: 0.2, end: 0);
  }
}

class _CategoryButton extends StatelessWidget {
  final Category category;
  final bool isSelected;
  final VoidCallback onTap;
  final int animationDelay;

  const _CategoryButton({
    required this.category,
    required this.isSelected,
    required this.onTap,
    required this.animationDelay,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        decoration: BoxDecoration(
          color: isSelected 
              ? category.primaryColor.withOpacity(0.2)
              : Colors.white.withOpacity(0.02),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected 
                ? category.primaryColor.withOpacity(0.5)
                : Colors.white.withOpacity(0.1),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: category.primaryColor.withOpacity(0.3),
              blurRadius: 12,
              spreadRadius: 2,
            ),
          ] : [],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon with emoji
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: isSelected 
                    ? category.primaryColor.withOpacity(0.2)
                    : Colors.grey[800]?.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Text(
                  category.iconEmoji,
                  style: const TextStyle(fontSize: 24),
                ),
              ),
            ),
            const SizedBox(height: 8),
            // Category name
            Text(
              category.name,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                color: isSelected ? Colors.white : Colors.grey[300],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    ).animate().fadeIn(
      duration: 300.ms,
      delay: Duration(milliseconds: animationDelay),
    ).scale(begin: const Offset(0.8, 0.8), end: const Offset(1.0, 1.0));
  }
}

class _NarratorButton extends StatelessWidget {
  final String text;
  final bool isSelected;
  final VoidCallback onTap;

  const _NarratorButton({
    required this.text,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFc039ff) : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          text,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            color: isSelected ? Colors.white : Colors.grey[400],
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
