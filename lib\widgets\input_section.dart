import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../providers/app_provider.dart';

class InputSection extends StatefulWidget {
  const InputSection({super.key});

  @override
  State<InputSection> createState() => _InputSectionState();
}

class _InputSectionState extends State<InputSection> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        return Column(
          children: [
            // Input field with auto-suggestions
            Stack(
              children: [
                // Main input field
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _controller,
                        focusNode: _focusNode,
                        enabled: provider.currentCategory != null,
                        decoration: InputDecoration(
                          hintText: provider.currentCategory?.placeholder ?? 
                                   'اختر قسماً أولاً...',
                          suffixIcon: _controller.text.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    _controller.clear();
                                    provider.hideSuggestions();
                                  },
                                )
                              : null,
                        ),
                        onChanged: (value) {
                          provider.showAutoSuggestions(value);
                          setState(() {}); // Update clear button visibility
                        },
                        onSubmitted: (value) {
                          if (value.trim().isNotEmpty) {
                            _generateIdeas(provider, value.trim());
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Generate button
                    ElevatedButton(
                      onPressed: provider.currentCategory != null && 
                                _controller.text.trim().isNotEmpty &&
                                !provider.isLoading
                          ? () => _generateIdeas(provider, _controller.text.trim())
                          : null,
                      child: provider.isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Text('ألهمني'),
                    ),
                  ],
                ),
                
                // Auto-suggestions dropdown
                if (provider.autoSuggestions.isNotEmpty)
                  Positioned(
                    top: 60,
                    left: 0,
                    right: 0,
                    child: _buildSuggestionsDropdown(provider),
                  ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Dynamic suggestions buttons
            if (provider.currentCategory != null)
              _buildDynamicSuggestions(provider),
            
            // Error message
            if (provider.errorMessage.isNotEmpty)
              Container(
                margin: const EdgeInsets.only(top: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.error_outline, color: Colors.red, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        provider.errorMessage,
                        style: const TextStyle(color: Colors.red),
                      ),
                    ),
                  ],
                ),
              ).animate().fadeIn(duration: 300.ms).shake(),
          ],
        );
      },
    );
  }

  Widget _buildSuggestionsDropdown(AppProvider provider) {
    return Container(
      constraints: const BoxConstraints(maxHeight: 200),
      decoration: BoxDecoration(
        color: Colors.grey[800]?.withOpacity(0.95),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFc039ff).withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: provider.autoSuggestions.length,
        itemBuilder: (context, index) {
          final suggestion = provider.autoSuggestions[index];
          final isSelected = provider.currentSuggestionIndex == index;
          
          return InkWell(
            onTap: () {
              _controller.text = suggestion;
              provider.hideSuggestions();
              _focusNode.unfocus();
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isSelected 
                    ? const Color(0xFFc039ff).withOpacity(0.15)
                    : null,
                border: index < provider.autoSuggestions.length - 1
                    ? Border(
                        bottom: BorderSide(
                          color: Colors.white.withOpacity(0.05),
                        ),
                      )
                    : null,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    provider.currentCategory?.name ?? '',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey[400],
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    suggestion,
                    style: TextStyle(
                      fontSize: 14,
                      color: isSelected ? Colors.white : Colors.grey[300],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    ).animate().fadeIn(duration: 200.ms).slideY(begin: -0.2, end: 0);
  }

  Widget _buildDynamicSuggestions(AppProvider provider) {
    final suggestions = provider.currentCategory!.suggestions.take(3).toList();
    
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: suggestions.asMap().entries.map((entry) {
        final index = entry.key;
        final suggestion = entry.value;
        
        return InkWell(
          onTap: () {
            _controller.text = suggestion;
            provider.hideSuggestions();
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.grey[700]?.withOpacity(0.5),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: const Color(0xFFc039ff).withOpacity(0.2),
              ),
            ),
            child: Text(
              suggestion,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[300],
              ),
            ),
          ),
        ).animate().fadeIn(
          duration: 300.ms,
          delay: Duration(milliseconds: index * 100),
        ).scale(begin: const Offset(0.8, 0.8), end: const Offset(1.0, 1.0));
      }).toList(),
    );
  }

  void _generateIdeas(AppProvider provider, String topic) {
    provider.generateIdeas(topic);
    _focusNode.unfocus();
  }
}
