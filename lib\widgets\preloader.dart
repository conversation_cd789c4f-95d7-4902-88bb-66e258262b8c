import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math' as math;

class Preloader extends StatefulWidget {
  const Preloader({super.key});

  @override
  State<Preloader> createState() => _PreloaderState();
}

class _PreloaderState extends State<Preloader>
    with TickerProviderStateMixin {
  late AnimationController _eyeController;
  late AnimationController _floatController;

  @override
  void initState() {
    super.initState();
    
    _eyeController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    )..repeat();
    
    _floatController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _eyeController.dispose();
    _floatController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: RadialGradient(
            center: const Alignment(0.6, 0.4),
            colors: [
              const Color(0xFFc039ff).withOpacity(0.2),
              const Color(0xFF6a11cb).withOpacity(0.1),
              const Color(0xFF02041a),
            ],
            stops: const [0.0, 0.4, 1.0],
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFc039ff).withOpacity(0.2),
              blurRadius: 40,
              spreadRadius: 10,
            ),
          ],
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Animated brain icon
              AnimatedBuilder(
                animation: _floatController,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(0, -5 * math.sin(_floatController.value * math.pi)),
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(30),
                        gradient: const LinearGradient(
                          colors: [Color(0xFFc039ff), Color(0xFF6a11cb)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFFc039ff).withOpacity(0.7),
                            blurRadius: 20,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: Stack(
                        children: [
                          // Brain shape
                          const Center(
                            child: Icon(
                              Icons.psychology,
                              size: 60,
                              color: Colors.white,
                            ),
                          ),
                          
                          // Animated eyes
                          AnimatedBuilder(
                            animation: _eyeController,
                            builder: (context, child) {
                              double eyeX = 10 * math.sin(_eyeController.value * 2 * math.pi);
                              double eyeY = 5 * math.cos(_eyeController.value * 2 * math.pi);
                              
                              return Stack(
                                children: [
                                  // Left eye
                                  Positioned(
                                    left: 35 + eyeX,
                                    top: 45 + eyeY,
                                    child: Container(
                                      width: 8,
                                      height: 8,
                                      decoration: const BoxDecoration(
                                        color: Colors.white,
                                        shape: BoxShape.circle,
                                      ),
                                    ),
                                  ),
                                  // Right eye
                                  Positioned(
                                    right: 35 + eyeX,
                                    top: 45 + eyeY,
                                    child: Container(
                                      width: 8,
                                      height: 8,
                                      decoration: const BoxDecoration(
                                        color: Colors.white,
                                        shape: BoxShape.circle,
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
              
              const SizedBox(height: 24),
              
              // App title with gradient animation
              ShaderMask(
                shaderCallback: (bounds) => const LinearGradient(
                  colors: [
                    Color(0xFFc039ff),
                    Color(0xFF6a11cb),
                    Color(0xFFf871b2),
                  ],
                ).createShader(bounds),
                child: const Text(
                  'العقل المبدع',
                  style: TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    letterSpacing: 1.0,
                  ),
                ),
              ).animate().fadeIn(
                duration: 700.ms,
                delay: 300.ms,
              ).slideY(begin: 0.3, end: 0),
              
              const SizedBox(height: 12),
              
              // Version text
              Text(
                'الإصدار 2.0',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[400],
                  letterSpacing: 0.5,
                ),
              ).animate().fadeIn(
                duration: 500.ms,
                delay: 800.ms,
              ),
              
              const SizedBox(height: 40),
              
              // Loading indicator
              Container(
                width: 40,
                height: 40,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    const Color(0xFFc039ff).withOpacity(0.8),
                  ),
                  strokeWidth: 3,
                ),
              ).animate().fadeIn(
                duration: 500.ms,
                delay: 1000.ms,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
