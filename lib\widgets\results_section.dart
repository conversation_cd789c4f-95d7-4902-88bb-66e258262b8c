import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../providers/app_provider.dart';
import '../models/idea.dart';

class ResultsSection extends StatelessWidget {
  const ResultsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return _buildLoader();
        }
        
        if (provider.currentIdeas.isEmpty) {
          return _buildPlaceholder();
        }
        
        return _buildResults(provider);
      },
    );
  }

  Widget _buildLoader() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              gradient: const LinearGradient(
                colors: [Color(0xFFc039ff), Color(0xFF6a11cb)],
              ),
            ),
            child: const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                strokeWidth: 3,
              ),
            ),
          ).animate(onPlay: (controller) => controller.repeat())
           .rotate(duration: 1000.ms),
          
          const SizedBox(height: 16),
          
          Text(
            'العقل المبدع يفكر...',
            style: TextStyle(
              color: Colors.grey[400],
              fontSize: 16,
              letterSpacing: 1.5,
            ),
          ).animate(onPlay: (controller) => controller.repeat())
           .fadeIn(duration: 1000.ms)
           .then()
           .fadeOut(duration: 1000.ms),
        ],
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.lightbulb_outline,
            size: 48,
            color: Colors.grey[600],
          ),
          const SizedBox(height: 16),
          Text(
            'الأفكار التي سيتم إنشاؤها ستظهر هنا.',
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ).animate().fadeIn(duration: 500.ms, delay: 500.ms);
  }

  Widget _buildResults(AppProvider provider) {
    return Column(
      children: provider.currentIdeas.asMap().entries.map((entry) {
        final index = entry.key;
        final idea = entry.value;
        
        return _IdeaCard(
          idea: idea,
          provider: provider,
          animationDelay: index * 100,
        );
      }).toList(),
    );
  }
}

class _IdeaCard extends StatelessWidget {
  final Idea idea;
  final AppProvider provider;
  final int animationDelay;

  const _IdeaCard({
    required this.idea,
    required this.provider,
    required this.animationDelay,
  });

  @override
  Widget build(BuildContext context) {
    final category = provider.currentCategory!;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and copy button
              Row(
                children: [
                  Text(
                    category.iconEmoji,
                    style: const TextStyle(fontSize: 24),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      idea.title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        foreground: Paint()
                          ..shader = LinearGradient(
                            colors: [category.primaryColor, category.secondaryColor],
                          ).createShader(const Rect.fromLTWH(0, 0, 200, 70)),
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.copy, size: 20),
                    onPressed: () => _copyToClipboard(context, idea),
                    tooltip: 'نسخ الفكرة',
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Content
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[800]?.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                  border: Border(
                    right: BorderSide(
                      color: category.primaryColor.withOpacity(0.5),
                      width: 3,
                    ),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      idea.description,
                      style: TextStyle(
                        color: Colors.grey[300],
                        height: 1.6,
                      ),
                    ),
                    
                    // Expanded content (if available)
                    if (idea.isExpanded && idea.expandedContent != null) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: category.primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: category.primaryColor.withOpacity(0.3),
                          ),
                        ),
                        child: Text(
                          idea.expandedContent!,
                          style: TextStyle(
                            color: Colors.grey[200],
                            height: 1.6,
                          ),
                        ),
                      ).animate().fadeIn(duration: 500.ms).slideY(begin: 0.2, end: 0),
                    ],
                  ],
                ),
              ),
              
              const SizedBox(height: 12),
              
              // Expand button
              if (!idea.isExpanded)
                Align(
                  alignment: Alignment.centerLeft,
                  child: TextButton.icon(
                    onPressed: () => provider.expandIdea(idea, ''),
                    icon: Text(category.iconEmoji),
                    label: Text(category.expandButtonText),
                    style: TextButton.styleFrom(
                      foregroundColor: category.primaryColor,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    ).animate().fadeIn(
      duration: 400.ms,
      delay: Duration(milliseconds: animationDelay),
    ).slideY(begin: 0.3, end: 0);
  }

  void _copyToClipboard(BuildContext context, Idea idea) {
    String textToCopy = '${idea.title}\n\n${idea.description}';
    if (idea.isExpanded && idea.expandedContent != null) {
      textToCopy += '\n\n${idea.expandedContent}';
    }
    
    Clipboard.setData(ClipboardData(text: textToCopy));
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('تم نسخ الفكرة'),
        backgroundColor: provider.currentCategory!.primaryColor,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
